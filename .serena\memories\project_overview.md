# Project Overview

## Purpose
This is a Python-based passphrase generator project that provides two main functionalities:
1. Generate passphrases by combining personal information (nickname, <PERSON><PERSON>, first love's name, favorite food) with random separators and case changes
2. Encrypt single words (12-24 characters) by adding random numbers, special characters, and case changes

## Tech Stack
- Python 3.x
- Standard library modules:
  - random
  - string
  - typing

## Project Structure
```
passphrase/
├── main.py                 # CLI interface
└── passphrase_generator.py # Core functionality
```

## Key Components
1. PassphraseGenerator class (passphrase_generator.py)
   - Handles personal info storage
   - Generates random separators
   - Implements word encryption
   - Manages passphrase generation

2. CLI Interface (main.py)
   - User input handling
   - Menu-driven interface
   - Error handling