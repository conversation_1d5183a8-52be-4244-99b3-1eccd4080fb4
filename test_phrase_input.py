#!/usr/bin/env python3
"""
Test script to verify the phrase input field works correctly.
"""

import tkinter as tk
from passphrase_gui import PassphraseG<PERSON>

def test_phrase_input():
    """Test that the phrase input field accepts input properly."""
    print("Testing phrase input functionality...")
    
    root = tk.Tk()
    root.withdraw()  # Hide window for testing
    
    try:
        # Create GUI
        app = PassphraseGUI(root)
        
        # Test phrase entry exists and is accessible
        assert hasattr(app, 'phrase_entry'), "Phrase entry not found"

        # Test input functionality
        test_phrase = "MyTestPhrase123"
        app.phrase_entry.insert(0, test_phrase)

        # Verify input was accepted
        entered_text = app.phrase_entry.get()
        assert entered_text == test_phrase, f"Input failed: expected '{test_phrase}', got '{entered_text}'"

        # Test clearing and re-entering
        app.phrase_entry.delete(0, tk.END)
        app.phrase_entry.insert(0, "NewTestPhrase")
        new_text = app.phrase_entry.get()
        assert new_text == "NewTestPhrase", "Clear and re-enter failed"

        print("✅ Phrase input field works correctly!")
        print(f"✅ Successfully entered: '{test_phrase}'")
        print("✅ Clear and re-enter works!")
        print("✅ Simple input field is functional!")
        
    except Exception as e:
        print(f"❌ Phrase input test failed: {e}")
        raise
    finally:
        root.destroy()

def test_encryption_with_phrase():
    """Test that encryption works with phrase input."""
    print("\nTesting encryption with phrase input...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = PassphraseGUI(root)
        
        # Set up test scenario
        app.generator.use_legacy_questions()
        
        # Set test answers
        test_answers = {
            'nickname': 'TestNick',
            'pin': '1234',
            'first_love': 'TestLove',
            'favorite_food': 'Pizza'
        }
        
        for key, value in test_answers.items():
            app.generator.set_personal_info(key, value)
        
        # Set test phrase
        test_phrase = "MySecretPhrase"
        app.phrase_entry.insert(0, test_phrase)
        
        # Verify phrase can be retrieved
        retrieved_phrase = app.phrase_entry.get().strip()
        assert retrieved_phrase == test_phrase, f"Phrase retrieval failed: got '{retrieved_phrase}'"
        
        # Test encryption (simulate the encrypt button logic)
        encrypted, order, shift = app.generator.encrypt_phrase(retrieved_phrase)
        
        # Verify encryption worked
        assert isinstance(encrypted, str) and len(encrypted) > 0, "Encryption failed"
        assert isinstance(order, list) and len(order) > 0, "Order not generated"
        assert isinstance(shift, int), "Shift not generated"
        
        print("✅ Phrase input works with encryption!")
        print(f"✅ Original phrase: '{test_phrase}'")
        print(f"✅ Retrieved phrase: '{retrieved_phrase}'")
        print("✅ Encryption successful!")
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        raise
    finally:
        root.destroy()

def main():
    """Run all phrase input tests."""
    print("=" * 50)
    print("TESTING PHRASE INPUT FUNCTIONALITY")
    print("=" * 50)
    
    try:
        test_phrase_input()
        test_encryption_with_phrase()
        
        print("\n" + "=" * 50)
        print("✅ ALL PHRASE INPUT TESTS PASSED!")
        print("=" * 50)
        print("\nPhrase input improvements:")
        print("• Removed password masking by default")
        print("• Added visibility toggle button (👁️)")
        print("• Better styling and layout")
        print("• Clear instructions")
        print("• Proper input validation")
        print("\nThe phrase input field should now work perfectly!")
        
    except Exception as e:
        print(f"\n❌ PHRASE INPUT TESTS FAILED: {e}")
        raise

if __name__ == "__main__":
    main()
