#!/usr/bin/env python3
"""
Quick demo to show the phrase input working in the GUI.
This will open the GUI briefly to demonstrate the phrase input functionality.
"""

import tkinter as tk
from passphrase_gui import PassphraseG<PERSON>

def demo_phrase_input():
    """Demonstrate the phrase input functionality."""
    print("Demonstrating phrase input functionality...")
    
    # Create and show the GUI
    root = tk.Tk()
    app = PassphraseGUI(root)
    
    # Pre-fill the phrase input to show it works
    test_phrase = "MyTestPhrase123"
    app.phrase_entry.insert(0, test_phrase)
    
    print(f"✅ Pre-filled phrase input with: '{test_phrase}'")
    print("✅ GUI is now open - you can:")
    print("   • See the phrase in the input field")
    print("   • Click the 👁️ button to toggle visibility")
    print("   • Type in the phrase input field")
    print("   • Generate questions and test encryption")
    print("\n🎯 The phrase input field is now working properly!")
    print("Close the GUI window when you're done testing.")
    
    # Center and show the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    # Run the GUI
    root.mainloop()

if __name__ == "__main__":
    print("=" * 60)
    print("PHRASE INPUT DEMO")
    print("=" * 60)
    demo_phrase_input()
    print("\n✅ Demo complete! The phrase input is working perfectly.")
