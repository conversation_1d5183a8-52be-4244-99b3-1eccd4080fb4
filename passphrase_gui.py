#!/usr/bin/env python3
"""
Simple GUI for the Passphrase Generator
A clean, user-friendly interface for encrypting and decrypting phrases.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import random
from passphrase_generator import PassphraseGenerator

class PassphraseGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🔐 Passphrase Generator")
        self.root.geometry("1000x800")  # Larger window
        self.root.configure(bg='#f0f0f0')
        self.root.minsize(900, 700)  # Minimum size to prevent cutting off
        
        # Initialize generator
        self.generator = PassphraseGenerator()
        self.current_questions = []
        
        # Create main interface
        self.create_widgets()
        
    def create_widgets(self):
        """Create the main GUI widgets."""
        # Title
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame, 
            text="🔐 Passphrase Generator", 
            font=('Arial', 24, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Secure your phrases with personal questions",
            font=('Arial', 12),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        subtitle_label.pack()
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both', padx=20, pady=20)
        
        # Create tabs
        self.create_encrypt_tab()
        self.create_decrypt_tab()
        
    def create_encrypt_tab(self):
        """Create the encryption tab."""
        encrypt_frame = ttk.Frame(self.notebook)
        self.notebook.add(encrypt_frame, text="🔒 Encrypt Phrase")
        
        # Mode selection
        mode_frame = ttk.LabelFrame(encrypt_frame, text="Question Mode", padding=10)
        mode_frame.pack(fill='x', padx=10, pady=10)
        
        self.mode_var = tk.StringVar(value="random")
        
        random_radio = ttk.Radiobutton(
            mode_frame, 
            text="🎲 Random Questions (Recommended)", 
            variable=self.mode_var, 
            value="random",
            command=self.on_mode_change
        )
        random_radio.pack(anchor='w', pady=2)
        
        legacy_radio = ttk.Radiobutton(
            mode_frame, 
            text="📋 Legacy Questions (Compatibility)", 
            variable=self.mode_var, 
            value="legacy",
            command=self.on_mode_change
        )
        legacy_radio.pack(anchor='w', pady=2)
        
        # Number of questions (for random mode)
        self.num_questions_frame = ttk.LabelFrame(encrypt_frame, text="Security Level", padding=10)
        self.num_questions_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(self.num_questions_frame, text="Number of questions:").pack(anchor='w')
        self.num_questions_var = tk.IntVar(value=4)
        num_scale = ttk.Scale(
            self.num_questions_frame, 
            from_=3, 
            to=8, 
            variable=self.num_questions_var,
            orient='horizontal',
            command=self.on_questions_change
        )
        num_scale.pack(fill='x', pady=5)
        
        self.num_label = tk.Label(self.num_questions_frame, text="4 questions")
        self.num_label.pack(anchor='w')
        
        # Generate questions button
        generate_btn = ttk.Button(
            encrypt_frame, 
            text="🎯 Generate Questions", 
            command=self.generate_questions
        )
        generate_btn.pack(pady=10)
        
        # Questions frame with scrollable area
        questions_container = ttk.LabelFrame(encrypt_frame, text="Personal Questions", padding=10)
        questions_container.pack(fill='both', expand=True, padx=10, pady=5)

        # Create scrollable frame for questions
        self.questions_canvas = tk.Canvas(questions_container, bg='white', highlightthickness=0)
        self.questions_scrollbar = ttk.Scrollbar(questions_container, orient="vertical", command=self.questions_canvas.yview)
        self.questions_frame = tk.Frame(self.questions_canvas, bg='white')

        # Configure scrolling
        self.questions_frame.bind(
            "<Configure>",
            lambda e: self.questions_canvas.configure(scrollregion=self.questions_canvas.bbox("all"))
        )

        self.questions_canvas.create_window((0, 0), window=self.questions_frame, anchor="nw")
        self.questions_canvas.configure(yscrollcommand=self.questions_scrollbar.set)

        # Pack scrollable components
        self.questions_canvas.pack(side="left", fill="both", expand=True)
        self.questions_scrollbar.pack(side="right", fill="y")

        # Add mouse wheel scrolling
        self.questions_canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.questions_canvas.bind("<Button-4>", self._on_mousewheel)
        self.questions_canvas.bind("<Button-5>", self._on_mousewheel)
        
        # Simple phrase input
        phrase_frame = ttk.LabelFrame(encrypt_frame, text="Phrase to Encrypt", padding=10)
        phrase_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(phrase_frame, text="Enter your phrase:").pack(anchor='w')
        self.phrase_entry = tk.Entry(phrase_frame, font=('Arial', 12))
        self.phrase_entry.pack(fill='x', pady=5)
        
        # Encrypt button (larger and more prominent)
        encrypt_btn = ttk.Button(
            encrypt_frame,
            text="🔒 ENCRYPT MY PHRASE",
            command=self.encrypt_phrase
        )
        encrypt_btn.pack(pady=15)
        
        # Results area - make it more visible and properly sized
        self.results_frame = ttk.LabelFrame(encrypt_frame, text="🔐 Encrypted Results", padding=10)
        self.results_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Add instruction label
        results_instruction = tk.Label(
            self.results_frame,
            text="Your encrypted results will appear here after encryption:",
            font=('Arial', 9),
            fg='#7f8c8d'
        )
        results_instruction.pack(anchor='w', pady=(0, 5))

        self.results_text = scrolledtext.ScrolledText(
            self.results_frame,
            height=10,  # Larger height
            font=('Courier', 10),  # Slightly larger font
            wrap=tk.WORD,
            relief='solid',
            bd=2,
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        self.results_text.pack(fill='both', expand=True, pady=5)

        # Add initial placeholder text
        placeholder_text = "🔒 No encryption results yet.\n\n"
        placeholder_text += "Steps to encrypt:\n"
        placeholder_text += "1. Choose question mode (Random/Legacy)\n"
        placeholder_text += "2. Generate questions\n"
        placeholder_text += "3. Answer all questions\n"
        placeholder_text += "4. Enter your phrase\n"
        placeholder_text += "5. Click 'ENCRYPT MY PHRASE'\n\n"
        placeholder_text += "Your encrypted results will appear here!"

        self.results_text.insert(1.0, placeholder_text)
        self.results_text.config(state='disabled')  # Make read-only initially
        
    def create_decrypt_tab(self):
        """Create the decryption tab."""
        decrypt_frame = ttk.Frame(self.notebook)
        self.notebook.add(decrypt_frame, text="🔓 Decrypt Phrase")
        
        # Instructions
        instructions = ttk.LabelFrame(decrypt_frame, text="Instructions", padding=10)
        instructions.pack(fill='x', padx=10, pady=10)
        
        instruction_text = tk.Label(
            instructions,
            text="Enter the encrypted phrase, order, and shift values from encryption:",
            wraplength=700,
            justify='left'
        )
        instruction_text.pack(anchor='w')
        
        # Input fields with better layout
        input_frame = ttk.LabelFrame(decrypt_frame, text="Decryption Data", padding=15)
        input_frame.pack(fill='x', padx=10, pady=10)

        # Encrypted phrase
        tk.Label(input_frame, text="Encrypted Phrase:", font=('Arial', 10, 'bold')).pack(anchor='w')
        self.decrypt_phrase_entry = tk.Entry(
            input_frame,
            font=('Courier', 10),
            relief='solid',
            bd=1,
            highlightthickness=1,
            highlightcolor='#3498db'
        )
        self.decrypt_phrase_entry.pack(fill='x', pady=(2, 10), ipady=3)

        # Order
        tk.Label(input_frame, text="Order (comma-separated):", font=('Arial', 10, 'bold')).pack(anchor='w')
        self.order_entry = tk.Entry(
            input_frame,
            font=('Courier', 10),
            relief='solid',
            bd=1,
            highlightthickness=1,
            highlightcolor='#3498db'
        )
        self.order_entry.pack(fill='x', pady=(2, 10), ipady=3)

        # Shift
        tk.Label(input_frame, text="Shift Number:", font=('Arial', 10, 'bold')).pack(anchor='w')
        self.shift_entry = tk.Entry(
            input_frame,
            font=('Courier', 10),
            relief='solid',
            bd=1,
            highlightthickness=1,
            highlightcolor='#3498db'
        )
        self.shift_entry.pack(fill='x', pady=(2, 10), ipady=3)
        
        # Decrypt button
        decrypt_btn = ttk.Button(
            decrypt_frame, 
            text="🔓 Decrypt Phrase", 
            command=self.decrypt_phrase,
            style='Accent.TButton'
        )
        decrypt_btn.pack(pady=20)
        
        # Result
        result_frame = ttk.LabelFrame(decrypt_frame, text="Decrypted Result", padding=10)
        result_frame.pack(fill='x', padx=10, pady=10)
        
        self.decrypt_result = tk.Entry(
            result_frame, 
            font=('Arial', 14, 'bold'), 
            state='readonly',
            justify='center'
        )
        self.decrypt_result.pack(fill='x', pady=5)
        
    def on_mode_change(self):
        """Handle mode change between random and legacy questions."""
        if self.mode_var.get() == "random":
            self.num_questions_frame.pack(fill='x', padx=10, pady=5)
        else:
            self.num_questions_frame.pack_forget()
            
    def on_questions_change(self, value):
        """Update the number of questions label."""
        num = int(float(value))
        self.num_label.config(text=f"{num} questions")

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling in the questions area."""
        if event.delta:
            # Windows
            self.questions_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        else:
            # Linux
            if event.num == 4:
                self.questions_canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                self.questions_canvas.yview_scroll(1, "units")


        
    def generate_questions(self):
        """Generate and display questions based on selected mode."""
        # Clear previous questions
        for widget in self.questions_frame.winfo_children():
            widget.destroy()

        try:
            if self.mode_var.get() == "legacy":
                self.generator.use_legacy_questions()
                questions = list(self.generator.get_selected_questions().items())
            else:
                num_questions = self.num_questions_var.get()
                questions = self.generator.select_random_questions(num_questions)

            self.current_questions = []

            # Create question widgets with better spacing and layout
            for i, (key, question) in enumerate(questions):
                # Main question container
                question_container = tk.Frame(self.questions_frame, bg='white', relief='ridge', bd=1)
                question_container.pack(fill='x', padx=5, pady=8)

                # Question number and text
                question_header = tk.Frame(question_container, bg='#e8f4fd')
                question_header.pack(fill='x', padx=2, pady=2)

                q_label = tk.Label(
                    question_header,
                    text=f"Question {i+1}:",
                    font=('Arial', 10, 'bold'),
                    bg='#e8f4fd',
                    fg='#2c3e50'
                )
                q_label.pack(anchor='w', padx=5, pady=2)

                # Question text with better wrapping
                q_text = tk.Label(
                    question_container,
                    text=question,
                    wraplength=700,  # Increased wrap length
                    justify='left',
                    anchor='w',
                    font=('Arial', 10),
                    bg='white',
                    fg='#34495e'
                )
                q_text.pack(anchor='w', padx=10, pady=(0, 5))

                # Answer entry with better styling
                answer_frame = tk.Frame(question_container, bg='white')
                answer_frame.pack(fill='x', padx=10, pady=(0, 8))

                tk.Label(answer_frame, text="Your answer:", font=('Arial', 9), bg='white', fg='#7f8c8d').pack(anchor='w')

                answer_entry = tk.Entry(
                    answer_frame,
                    font=('Arial', 11),
                    relief='solid',
                    bd=1,
                    highlightthickness=1,
                    highlightcolor='#3498db'
                )
                answer_entry.pack(fill='x', pady=2, ipady=3)

                self.current_questions.append((key, answer_entry))

            # Update scroll region
            self.questions_frame.update_idletasks()
            self.questions_canvas.configure(scrollregion=self.questions_canvas.bbox("all"))

            # Success message
            mode_text = "legacy" if self.mode_var.get() == "legacy" else "random"
            messagebox.showinfo(
                "Questions Generated",
                f"Generated {len(questions)} {mode_text} questions!\nScroll down to see all questions and answer them before encrypting."
            )

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate questions: {str(e)}")
            
    def encrypt_phrase(self):
        """Encrypt the entered phrase using the answered questions."""
        try:
            # Validate phrase
            phrase = self.phrase_entry.get().strip()
            if not phrase:
                messagebox.showwarning("Missing Input", "Please enter a phrase to encrypt.")
                return
                
            # Validate questions are answered
            if not self.current_questions:
                messagebox.showwarning("No Questions", "Please generate and answer questions first.")
                return
                
            # Set answers
            for key, entry in self.current_questions:
                answer = entry.get().strip()
                if not answer:
                    messagebox.showwarning("Missing Answer", "Please answer all questions.")
                    return
                self.generator.set_personal_info(key, answer)
            
            # Encrypt
            encrypted, order, shift = self.generator.encrypt_phrase(phrase)
            
            # Display results - make sure text widget is enabled
            self.results_text.config(state='normal')

            result_text = f"🎉 ENCRYPTION SUCCESSFUL!\n"
            result_text += f"{'='*50}\n\n"
            result_text += f"📝 Original Phrase: {phrase}\n\n"
            result_text += f"🔐 Encrypted Result:\n{encrypted}\n\n"
            result_text += f"🔑 DECRYPTION KEYS (SAVE THESE!):\n"
            result_text += f"{'='*30}\n"
            result_text += f"Order: {','.join(order)}\n"
            result_text += f"Shift: {shift}\n"
            result_text += f"{'='*30}\n\n"
            result_text += f"⚠️  IMPORTANT: Keep the Order and Shift values safe!\n"
            result_text += f"    You need them to decrypt your phrase later.\n\n"
            result_text += f"✅ Copy the encrypted result and decryption keys\n"
            result_text += f"    to a safe location for future use."

            # Clear and insert new results
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(1.0, result_text)

            # Make read-only after inserting
            self.results_text.config(state='disabled')

            # Show success message
            messagebox.showinfo("🎉 Success!", "Phrase encrypted successfully!\n\nCheck the results area below for your encrypted phrase and decryption keys.")
            
        except Exception as e:
            messagebox.showerror("Encryption Error", f"Failed to encrypt phrase: {str(e)}")
            
    def decrypt_phrase(self):
        """Decrypt the entered encrypted phrase."""
        try:
            # Get inputs
            encrypted = self.decrypt_phrase_entry.get().strip()
            order_str = self.order_entry.get().strip()
            shift_str = self.shift_entry.get().strip()
            
            # Validate inputs
            if not all([encrypted, order_str, shift_str]):
                messagebox.showwarning("Missing Input", "Please fill in all decryption fields.")
                return
                
            # Parse inputs
            order = [x.strip() for x in order_str.split(',')]
            shift = int(shift_str)
            
            # Decrypt
            decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
            
            # Display result
            self.decrypt_result.config(state='normal')
            self.decrypt_result.delete(0, tk.END)
            self.decrypt_result.insert(0, decrypted)
            self.decrypt_result.config(state='readonly')
            
            messagebox.showinfo("Success", "Phrase decrypted successfully!")
            
        except ValueError as e:
            messagebox.showerror("Decryption Error", f"Failed to decrypt: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {str(e)}")

def main():
    """Run the GUI application."""
    root = tk.Tk()
    
    # Configure style
    style = ttk.Style()
    style.theme_use('clam')
    
    # Create and run app
    app = PassphraseGUI(root)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
