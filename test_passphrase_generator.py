#!/usr/bin/env python3
"""
Test script for the enhanced PassphraseGenerator with random questions.
This script validates both the new random question system and backward compatibility.
"""

from passphrase_generator import PassphraseGenerator

def test_random_question_selection():
    """Test the random question selection functionality."""
    print("Testing random question selection...")
    
    generator = PassphraseGenerator()
    
    # Test selecting different numbers of questions
    for num_questions in [3, 4, 5, 6]:
        print(f"\nTesting with {num_questions} questions:")
        
        selected = generator.select_random_questions(num_questions)
        print(f"Selected {len(selected)} questions:")
        
        for key, question in selected:
            print(f"  - {key}: {question}")
        
        # Verify the correct number was selected
        assert len(selected) == num_questions, f"Expected {num_questions}, got {len(selected)}"
        
        # Verify personal_info was initialized
        assert len(generator.personal_info) == num_questions, "personal_info not properly initialized"
        
        # Verify all values are empty initially
        assert all(value == '' for value in generator.personal_info.values()), "personal_info should be empty initially"

def test_legacy_compatibility():
    """Test backward compatibility with legacy questions."""
    print("\n\nTesting legacy compatibility...")
    
    generator = PassphraseGenerator()
    generator.use_legacy_questions()
    
    expected_keys = ['nickname', 'pin', 'first_love', 'favorite_food']
    
    # Verify legacy questions are set correctly
    assert set(generator.personal_info.keys()) == set(expected_keys), "Legacy questions not set correctly"
    
    print("Legacy questions loaded:")
    for key, question in generator.get_selected_questions().items():
        print(f"  - {key}: {question}")

def test_encryption_with_random_questions():
    """Test encryption/decryption with randomly selected questions."""
    print("\n\nTesting encryption with random questions...")
    
    generator = PassphraseGenerator()
    
    # Select 4 random questions
    selected = generator.select_random_questions(4)
    
    # Set some test answers
    test_answers = {
        list(generator.personal_info.keys())[0]: "TestAnswer1",
        list(generator.personal_info.keys())[1]: "TestAnswer2", 
        list(generator.personal_info.keys())[2]: "TestAnswer3",
        list(generator.personal_info.keys())[3]: "TestAnswer4"
    }
    
    for key, value in test_answers.items():
        generator.set_personal_info(key, value)
    
    # Test encryption
    test_phrase = "MySecretPhrase"
    encrypted, order, shift = generator.encrypt_phrase(test_phrase)
    
    print(f"Original phrase: {test_phrase}")
    print(f"Encrypted: {encrypted}")
    print(f"Order: {order}")
    print(f"Shift: {shift}")
    
    # Test decryption
    decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
    print(f"Decrypted: {decrypted}")
    
    # Verify decryption worked
    assert decrypted == test_phrase, f"Decryption failed: expected '{test_phrase}', got '{decrypted}'"
    print("✓ Encryption/decryption test passed!")

def test_question_pool_coverage():
    """Test that the question pool has good coverage across categories."""
    print("\n\nTesting question pool coverage...")
    
    generator = PassphraseGenerator()
    all_questions = generator.get_all_available_questions()
    
    print("Available question categories:")
    total_questions = 0
    for category, questions in all_questions.items():
        print(f"  - {category}: {len(questions)} questions")
        total_questions += len(questions)
    
    print(f"Total questions available: {total_questions}")
    
    # Verify we have enough questions for variety
    assert total_questions >= 20, "Should have at least 20 questions for good variety"
    
    # Verify all categories have questions
    for category, questions in all_questions.items():
        assert len(questions) > 0, f"Category '{category}' has no questions"

def test_randomness():
    """Test that question selection is actually random."""
    print("\n\nTesting randomness of question selection...")
    
    generator = PassphraseGenerator()
    
    # Generate multiple sets of questions and verify they're different
    question_sets = []
    for i in range(5):
        selected = generator.select_random_questions(4)
        question_keys = [key for key, _ in selected]
        question_sets.append(set(question_keys))
    
    # Check that we got some variety (not all sets should be identical)
    unique_sets = len(set(frozenset(s) for s in question_sets))
    print(f"Generated {unique_sets} unique question sets out of 5 attempts")
    
    # With enough questions in the pool, we should get some variety
    # (This might occasionally fail due to randomness, but should usually pass)
    if unique_sets > 1:
        print("✓ Randomness test passed - got variety in question selection")
    else:
        print("⚠ Randomness test: all sets were identical (might be due to chance)")

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING ENHANCED PASSPHRASE GENERATOR")
    print("=" * 60)
    
    try:
        test_random_question_selection()
        test_legacy_compatibility()
        test_encryption_with_random_questions()
        test_question_pool_coverage()
        test_randomness()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED SUCCESSFULLY! ✓")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        raise

if __name__ == "__main__":
    main()
