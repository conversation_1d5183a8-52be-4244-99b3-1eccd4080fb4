# Suggested Commands

## Running the Application
```powershell
# Run the application
python main.py
```

## Development Commands
```powershell
# List files
dir
ls

# Change directory
cd <directory>

# Find files
Get-ChildItem -Recurse -Filter *.py

# Search in files
Select-String -Path *.py -Pattern "search_term"

# Git commands (if using version control)
git status
git add .
git commit -m "message"
git push
```

## Code Quality Commands
```powershell
# Install development dependencies (recommended)
pip install black pylint mypy

# Format code
black .

# Type checking
mypy .

# Linting
pylint *.py
```

## Testing (Future Enhancement)
```powershell
# Install pytest (when tests are added)
pip install pytest

# Run tests (when implemented)
pytest
```