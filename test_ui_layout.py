#!/usr/bin/env python3
"""
Quick test to verify the UI layout improvements work correctly.
This will open the GUI briefly to test question display.
"""

import tkinter as tk
from passphrase_gui import PassphraseGUI
import time

def test_layout():
    """Test the improved layout with multiple questions."""
    print("Testing improved UI layout...")
    
    # Create root window
    root = tk.Tk()
    
    try:
        # Create GUI
        app = PassphraseGUI(root)
        
        # Simulate generating 6 questions to test scrolling
        app.mode_var.set("random")
        app.num_questions_var.set(6)
        
        # Generate questions programmatically
        questions = app.generator.select_random_questions(6)
        app.current_questions = []
        
        # Clear and populate questions frame
        for widget in app.questions_frame.winfo_children():
            widget.destroy()
        
        # Create test questions with the new layout
        for i, (key, question) in enumerate(questions):
            # Main question container
            question_container = tk.Frame(app.questions_frame, bg='white', relief='ridge', bd=1)
            question_container.pack(fill='x', padx=5, pady=8)
            
            # Question header
            question_header = tk.Frame(question_container, bg='#e8f4fd')
            question_header.pack(fill='x', padx=2, pady=2)
            
            q_label = tk.Label(
                question_header, 
                text=f"Question {i+1}:",
                font=('Arial', 10, 'bold'),
                bg='#e8f4fd',
                fg='#2c3e50'
            )
            q_label.pack(anchor='w', padx=5, pady=2)
            
            # Question text
            q_text = tk.Label(
                question_container, 
                text=question,
                wraplength=700,
                justify='left',
                anchor='w',
                font=('Arial', 10),
                bg='white',
                fg='#34495e'
            )
            q_text.pack(anchor='w', padx=10, pady=(0, 5))
            
            # Answer entry
            answer_frame = tk.Frame(question_container, bg='white')
            answer_frame.pack(fill='x', padx=10, pady=(0, 8))
            
            tk.Label(answer_frame, text="Your answer:", font=('Arial', 9), bg='white', fg='#7f8c8d').pack(anchor='w')
            
            answer_entry = tk.Entry(
                answer_frame, 
                font=('Arial', 11),
                relief='solid',
                bd=1,
                highlightthickness=1,
                highlightcolor='#3498db'
            )
            answer_entry.pack(fill='x', pady=2, ipady=3)
            answer_entry.insert(0, f"Test answer {i+1}")  # Add test data
            
            app.current_questions.append((key, answer_entry))
        
        # Update scroll region
        app.questions_frame.update_idletasks()
        app.questions_canvas.configure(scrollregion=app.questions_canvas.bbox("all"))
        
        # Update the window
        root.update()
        
        print(f"✅ Successfully created {len(questions)} questions with improved layout!")
        print("✅ Scrollable area configured!")
        print("✅ All questions should be visible and properly formatted!")
        
        # Show window briefly for visual verification
        print("\nShowing GUI for 3 seconds for visual verification...")
        root.deiconify()  # Make sure window is visible
        root.lift()       # Bring to front
        root.after(3000, root.quit)  # Close after 3 seconds
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Layout test failed: {e}")
        raise
    finally:
        try:
            root.destroy()
        except:
            pass

def main():
    """Run the layout test."""
    print("=" * 50)
    print("TESTING UI LAYOUT IMPROVEMENTS")
    print("=" * 50)
    
    test_layout()
    
    print("\n" + "=" * 50)
    print("✅ UI LAYOUT TEST COMPLETE!")
    print("=" * 50)
    print("\nImprovements made:")
    print("• Larger window size (1000x800)")
    print("• Scrollable questions area")
    print("• Better question formatting")
    print("• Mouse wheel scrolling support")
    print("• Improved input field styling")
    print("• Better spacing and layout")
    print("\nThe GUI should now display all questions properly!")

if __name__ == "__main__":
    main()
