# 🔐 Passphrase Generator GUI

A simple, clean desktop interface for the enhanced passphrase generator with random questions.

## 🚀 Quick Start

### Option 1: Double-click to run
- **Windows**: Double-click `run_gui.bat`
- **Mac/Linux**: Run `python passphrase_gui.py` in terminal

### Option 2: Command line
```bash
python passphrase_gui.py
```

## 📱 How to Use

### 🔒 **Encrypt Tab** - Create Secure Passphrases

1. **Choose Question Mode:**
   - **🎲 Random Questions**: Recommended for new passphrases (enhanced security)
   - **📋 Legacy Questions**: For compatibility with old passphrases

2. **Set Security Level** (Random mode only):
   - Use the slider to choose 3-8 questions
   - More questions = higher security

3. **Generate Questions:**
   - Click "🎯 Generate Questions"
   - Questions will appear below

4. **Answer Questions:**
   - Fill in all the personal questions
   - Your answers are used as encryption keys

5. **Enter Your Phrase:**
   - Type the phrase you want to encrypt
   - It will be hidden for security

6. **Encrypt:**
   - Click "🔒 Encrypt Phrase"
   - **Save the results!** You need them to decrypt later

### 🔓 **Decrypt Tab** - Recover Your Phrases

1. **Enter Decryption Data:**
   - **Encrypted Phrase**: The scrambled text from encryption
   - **Order**: The comma-separated order string
   - **Shift**: The shift number

2. **Decrypt:**
   - Click "🔓 Decrypt Phrase"
   - Your original phrase will appear

## 🎯 **Features**

### ✨ **User-Friendly Interface**
- Clean, modern design
- Tabbed interface for easy navigation
- Clear instructions and feedback
- Error handling with helpful messages

### 🔒 **Security Features**
- **36 different questions** across 5 categories
- **Random question selection** for unpredictability
- **Configurable security levels** (3-8 questions)
- **Legacy compatibility** for old passphrases
- **Password masking** for phrase input

### 📊 **Question Categories**
- **Childhood** (8 questions): First pet, school, toys, etc.
- **Family** (6 questions): Mother's maiden name, siblings, etc.
- **Preferences** (8 questions): Favorite food, color, movie, etc.
- **Memories** (7 questions): First job, car, wedding, etc.
- **Personal** (7 questions): Nickname, lucky number, hero, etc.

## 🛡️ **Security Benefits**

### 🔄 **Randomness**
- Different questions each time you encrypt
- Prevents pattern recognition attacks
- **1.4+ million combinations** with 4 questions

### 🎯 **Flexibility**
- Choose your security level (3-8 questions)
- Balance between security and convenience
- Legacy mode for backward compatibility

### 🔐 **Strong Encryption**
- Uses personal information as encryption keys
- Random separators and character shifting
- Multiple encryption variations generated

## 💡 **Tips for Best Security**

### ✅ **Do:**
- Use **random questions mode** for new passphrases
- Choose **4-6 questions** for good security
- **Save your encryption data** safely (order & shift)
- Use **memorable but specific** answers
- Keep your **encrypted results** backed up

### ❌ **Don't:**
- Don't lose your order and shift values
- Don't use the same answers for different encryptions
- Don't share your personal question answers
- Don't use easily guessable answers

## 🔧 **Technical Requirements**

- **Python 3.6+** (comes with tkinter)
- **No additional installations** required
- **Cross-platform** (Windows, Mac, Linux)

## 📁 **Files**

- `passphrase_gui.py` - Main GUI application
- `passphrase_generator.py` - Core encryption engine
- `run_gui.bat` - Windows launcher
- `main.py` - Command-line version (still available)

## 🆘 **Troubleshooting**

### GUI won't start:
- Make sure Python is installed
- Try running: `python --version`
- Run from command line to see error messages

### Can't decrypt:
- Check that order and shift values are correct
- Make sure encrypted phrase is complete
- Verify you're using the right encryption data

### Questions not generating:
- Try switching between random and legacy modes
- Restart the application if needed

## 🎉 **Enjoy Your Secure Passphrases!**

The GUI makes it easy to create and manage secure passphrases with enhanced security through random questions. Your data stays private and secure on your computer.
