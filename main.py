from passphrase_generator import PassphraseGenerator

def get_user_input(prompt: str) -> str:
    """Get input from user with validation."""
    while True:
        value = input(prompt).strip()
        if value:
            return value
        print("Please enter a non-empty value.")

def main():
    print("Welcome to the Phrase Encrypter/Decrypter!")
    print("\nWhat would you like to do?")
    print("1. Encrypt a phrase (requires personal info)")
    print("2. Decrypt a phrase")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1, 2, or 3): ")
    
    if choice == "1":
        # Only get personal info if encrypting
        generator = PassphraseGenerator()
        print("\nTo encrypt, we need your personal information:")
        generator.set_personal_info('nickname', get_user_input("Enter your nickname: "))
        generator.set_personal_info('pin', get_user_input("Enter your PIN: "))
        generator.set_personal_info('first_love', get_user_input("Enter your first love's name: "))
        generator.set_personal_info('favorite_food', get_user_input("Enter your favorite food: "))
        
        phrase = get_user_input("\nEnter the phrase you want to encrypt: ")
        try:
            # Generate multiple variations
            print("\nHere are 3 different encrypted versions of your phrase:")
            for i in range(3):
                encrypted, order, shift = generator.encrypt_phrase(phrase)
                print(f"\nVersion {i+1}:")
                print(f"Encrypted: {encrypted}")
                print("Save these for decryption:")
                print(f"Order: {','.join(order)}")
                print(f"Shift: {shift}")
                
                # Show an example decryption to verify it works
                print("\nVerifying decryption...")
                decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
                print(f"Original phrase: {phrase}")
                print(f"Decrypted phrase: {decrypted}")
                if decrypted != phrase:
                    print("WARNING: Decryption verification failed!")
                
        except ValueError as e:
            print(f"Error: {e}")
            
    elif choice == "2":
        # Decryption doesn't need personal info
        print("\nTo decrypt, you need:")
        print("1. The encrypted phrase")
        print("2. The order string that was given during encryption")
        print("3. The shift number that was given during encryption")
        
        try:
            encrypted = get_user_input("\nEnter the encrypted phrase: ")
            order_string = get_user_input("Enter the order string (comma-separated): ")
            shift = int(get_user_input("Enter the shift number: "))
            
            # Clean up the order string and split it
            order = [x.strip() for x in order_string.split(',')]
            
            print("\nDecrypting with:")
            print(f"Encrypted: {encrypted}")
            print(f"Order: {order}")
            print(f"Shift: {shift}")
            
            decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
            print("\nDecrypted phrase:")
            print(decrypted)
            
        except ValueError as e:
            print(f"\nError during decryption: {str(e)}")
            
    elif choice == "3":
        print("\nThank you for using the Phrase Encrypter/Decrypter!")
        
    else:
        print("Invalid choice. Please run the program again and select 1, 2, or 3.")

if __name__ == "__main__":
    main() 