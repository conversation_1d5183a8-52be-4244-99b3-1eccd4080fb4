from passphrase_generator import PassphraseGenerator

def get_user_input(prompt: str) -> str:
    """Get input from user with validation."""
    while True:
        value = input(prompt).strip()
        if value:
            return value
        print("Please enter a non-empty value.")

def main():
    print("Welcome to the Phrase Encrypter/Decrypter!")
    print("\nWhat would you like to do?")
    print("1. Encrypt a phrase (requires personal info)")
    print("2. Decrypt a phrase")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1, 2, or 3): ")
    
    if choice == "1":
        # Only get personal info if encrypting
        generator = PassphraseGenerator()

        # Ask user to choose question mode
        print("\nFor better security, we'll ask you some personal questions.")
        print("Choose your question mode:")
        print("1. Random questions (recommended for new passphrases)")
        print("2. Legacy questions (for compatibility with old passphrases)")

        mode_choice = input("\nEnter your choice (1 or 2): ")

        if mode_choice == "2":
            # Use legacy questions for backward compatibility
            generator.use_legacy_questions()
            print("\nUsing legacy questions for compatibility:")

            # Ask the original four questions
            for question_key, question_text in generator.get_selected_questions().items():
                answer = get_user_input(question_text)
                generator.set_personal_info(question_key, answer)

        else:
            # Use new random question system
            print("\nUsing random questions for enhanced security.")
            print("You can choose how many questions to answer (3-8 questions recommended):")

            while True:
                try:
                    num_questions = int(input("How many questions would you like to answer? (3-8): "))
                    if 3 <= num_questions <= 8:
                        break
                    else:
                        print("Please enter a number between 3 and 8.")
                except ValueError:
                    print("Please enter a valid number.")

            # Select random questions
            try:
                selected_questions = generator.select_random_questions(num_questions)
                print(f"\nGreat! Here are your {num_questions} randomly selected questions:")
                print("(These questions will be different each time for better security)")

                # Ask each selected question
                for question_key, question_text in selected_questions:
                    answer = get_user_input(f"\n{question_text} ")
                    generator.set_personal_info(question_key, answer)

            except ValueError as e:
                print(f"Error selecting questions: {e}")
                return
        
        phrase = get_user_input("\nEnter the phrase you want to encrypt: ")
        try:
            # Generate multiple variations
            print("\nHere are 3 different encrypted versions of your phrase:")
            for i in range(3):
                encrypted, order, shift = generator.encrypt_phrase(phrase)
                print(f"\nVersion {i+1}:")
                print(f"Encrypted: {encrypted}")
                print("Save these for decryption:")
                print(f"Order: {','.join(order)}")
                print(f"Shift: {shift}")
                
                # Show an example decryption to verify it works
                print("\nVerifying decryption...")
                decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
                print(f"Original phrase: {phrase}")
                print(f"Decrypted phrase: {decrypted}")
                if decrypted != phrase:
                    print("WARNING: Decryption verification failed!")
                
        except ValueError as e:
            print(f"Error: {e}")
            
    elif choice == "2":
        # Decryption doesn't need personal info
        print("\nTo decrypt, you need:")
        print("1. The encrypted phrase")
        print("2. The order string that was given during encryption")
        print("3. The shift number that was given during encryption")
        
        try:
            encrypted = get_user_input("\nEnter the encrypted phrase: ")
            order_string = get_user_input("Enter the order string (comma-separated): ")
            shift = int(get_user_input("Enter the shift number: "))
            
            # Clean up the order string and split it
            order = [x.strip() for x in order_string.split(',')]
            
            print("\nDecrypting with:")
            print(f"Encrypted: {encrypted}")
            print(f"Order: {order}")
            print(f"Shift: {shift}")
            
            decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
            print("\nDecrypted phrase:")
            print(decrypted)
            
        except ValueError as e:
            print(f"\nError during decryption: {str(e)}")
            
    elif choice == "3":
        print("\nThank you for using the Phrase Encrypter/Decrypter!")
        
    else:
        print("Invalid choice. Please run the program again and select 1, 2, or 3.")

if __name__ == "__main__":
    main() 