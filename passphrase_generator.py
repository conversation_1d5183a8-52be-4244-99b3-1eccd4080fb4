import random
import string
from typing import List, Dict

class PassphraseGenerator:
    def __init__(self):
        self.personal_info = {
            'nickname': '',
            'pin': '',
            'first_love': '',
            'favorite_food': ''
        }
        self.separators = ['@', '#', '$', '%', '&', '*', '-', '+', '!']
        
    def set_personal_info(self, info_type: str, value: str) -> None:
        """Set personal information for passphrase generation."""
        if info_type in self.personal_info:
            self.personal_info[info_type] = value
            
    def get_random_separator(self) -> str:
        """Generate a random separator for passphrase parts."""
        return random.choice(self.separators)
    
    def transform_text(self, text: str, shift: int) -> str:
        """Transform text by shifting characters."""
        # Simple shift cipher for each character
        result = ''
        for c in text:
            if c.isalpha():
                # Determine the base (97 for lowercase, 65 for uppercase)
                base = 97 if c.islower() else 65
                # Shift the character, keeping it in the same case
                result += chr((ord(c) - base + shift) % 26 + base)
            elif c.isdigit():
                # For digits, shift within 0-9
                result += str((int(c) + shift) % 10)
            else:
                result += c
        return result
    
    def untransform_text(self, text: str, shift: int) -> str:
        """Reverse the transformation of text."""
        # Simple shift cipher in reverse
        result = ''
        for c in text:
            if c.isalpha():
                # Determine the base (97 for lowercase, 65 for uppercase)
                base = 97 if c.islower() else 65
                # Unshift the character, keeping it in the same case
                result += chr((ord(c) - base - shift) % 26 + base)
            elif c.isdigit():
                # For digits, unshift within 0-9
                result += str((int(c) - shift) % 10)
            else:
                result += c
        return result
    
    def encrypt_phrase(self, phrase: str) -> tuple[str, list[str], int]:
        """Encrypt a phrase by combining it with personal info in random ways."""
        # Verify we have all required information
        if not all(self.personal_info.values()):
            raise ValueError("All personal information must be set before encrypting")
        
        # Generate a random shift value (1-15)
        shift = random.randint(1, 15)
        
        # Transform all parts
        transformed_parts = [
            ('phrase', self.transform_text(phrase, shift)),
            ('nickname', self.transform_text(self.personal_info['nickname'], shift)),
            ('pin', self.transform_text(self.personal_info['pin'], shift)),
            ('first_love', self.transform_text(self.personal_info['first_love'], shift)),
            ('favorite_food', self.transform_text(self.personal_info['favorite_food'], shift))
        ]
        
        # Shuffle the order
        random.shuffle(transformed_parts)
        
        # Keep track of the order for decryption
        order = [label for label, _ in transformed_parts]
        
        # Build the result with random separators
        result = []
        for i, (_, part) in enumerate(transformed_parts):
            result.append(part)
            if i < len(transformed_parts) - 1:
                result.append(self.get_random_separator())
        
        return ''.join(result), order, shift
    
    @staticmethod
    def decrypt_phrase(encrypted: str, order: list[str], shift: int) -> str:
        """Decrypt an encrypted phrase using the original order of parts."""
        try:
            # Clean up inputs
            encrypted = encrypted.strip()
            order = [o.strip() for o in order]
            
            # Split by any of the common separators
            parts = []
            current_part = ''
            for char in encrypted:
                if char in '@#$%&*-+!':
                    if current_part:  # only append non-empty parts
                        parts.append(current_part)
                        current_part = ''
                else:
                    current_part += char
            if current_part:  # append the last part
                parts.append(current_part)
            
            print(f"Debug - Parts found: {parts}")  # Debug info
            print(f"Debug - Order provided: {order}")  # Debug info
            
            if len(parts) != len(order):
                raise ValueError(f"Number of parts ({len(parts)}) doesn't match order length ({len(order)})")
            
            # Find the position of the original phrase
            try:
                phrase_position = order.index('phrase')
            except ValueError:
                raise ValueError("'phrase' not found in order list")
            
            # Get the transformed phrase
            transformed_phrase = parts[phrase_position]
            
            # Create a temporary generator to use untransform_text
            temp_generator = PassphraseGenerator()
            
            # Untransform and return the phrase
            return temp_generator.untransform_text(transformed_phrase, shift)
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}") 