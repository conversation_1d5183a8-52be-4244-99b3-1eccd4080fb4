import random
import string
from typing import List, Dict, Tuple

class PassphraseGenerator:
    def __init__(self):
        # Expanded question pool organized by categories for better security
        self.question_pool = {
            'childhood': [
                ('childhood_pet', 'What was the name of your first pet?'),
                ('childhood_friend', 'What was the name of your best childhood friend?'),
                ('birth_city', 'In what city were you born?'),
                ('elementary_school', 'What was the name of your elementary school?'),
                ('childhood_nickname', 'What was your childhood nickname?'),
                ('favorite_toy', 'What was your favorite childhood toy?'),
                ('first_teacher', 'What was your first teacher\'s last name?'),
                ('childhood_address', 'What street did you grow up on?'),
            ],
            'family': [
                ('mother_maiden', 'What is your mother\'s maiden name?'),
                ('father_middle', 'What is your father\'s middle name?'),
                ('oldest_sibling', 'What is your oldest sibling\'s name?'),
                ('grandmother_name', 'What was your grandmother\'s first name?'),
                ('family_tradition', 'What is a unique family tradition you have?'),
                ('family_pet', 'What was your family\'s first pet\'s name?'),
            ],
            'preferences': [
                ('favorite_food', 'What is your favorite food?'),
                ('favorite_color', 'What is your favorite color?'),
                ('favorite_movie', 'What is your favorite movie?'),
                ('favorite_book', 'What is your favorite book?'),
                ('favorite_song', 'What is your favorite song?'),
                ('favorite_season', 'What is your favorite season?'),
                ('favorite_sport', 'What is your favorite sport?'),
                ('dream_destination', 'What is your dream travel destination?'),
            ],
            'memories': [
                ('first_love', 'What was your first love\'s name?'),
                ('first_job', 'Where was your first job?'),
                ('first_car', 'What was the make of your first car?'),
                ('wedding_location', 'Where did you get married? (or want to)'),
                ('memorable_concert', 'What was your first concert?'),
                ('high_school_mascot', 'What was your high school mascot?'),
                ('college_major', 'What was your college major?'),
            ],
            'personal': [
                ('nickname', 'What is your nickname?'),
                ('pin', 'What is your favorite 4-digit number?'),
                ('lucky_number', 'What is your lucky number?'),
                ('middle_name', 'What is your middle name?'),
                ('hero_name', 'Who is your childhood hero?'),
                ('fear', 'What is your biggest fear?'),
                ('hobby', 'What is your favorite hobby?'),
            ]
        }

        # Current session's selected questions and answers
        self.selected_questions = {}
        self.personal_info = {}

        self.separators = ['@', '#', '$', '%', '&', '*', '-', '+', '!']

    def select_random_questions(self, num_questions: int = 4, categories: List[str] = None) -> List[Tuple[str, str]]:
        """
        Select random questions from the question pool.

        Args:
            num_questions: Number of questions to select (default: 4)
            categories: List of categories to select from (default: all categories)

        Returns:
            List of tuples (question_key, question_text)
        """
        if categories is None:
            categories = list(self.question_pool.keys())

        # Collect all questions from specified categories
        all_questions = []
        for category in categories:
            if category in self.question_pool:
                all_questions.extend(self.question_pool[category])

        if len(all_questions) < num_questions:
            raise ValueError(f"Not enough questions available. Requested {num_questions}, but only {len(all_questions)} available.")

        # Randomly select questions
        selected = random.sample(all_questions, num_questions)

        # Store the selected questions for this session
        self.selected_questions = {key: question for key, question in selected}

        # Initialize personal_info with empty values for selected questions
        self.personal_info = {key: '' for key, _ in selected}

        return selected

    def get_selected_questions(self) -> Dict[str, str]:
        """Get the currently selected questions for this session."""
        return self.selected_questions.copy()

    def set_personal_info(self, info_type: str, value: str) -> None:
        """Set personal information for passphrase generation."""
        if info_type in self.personal_info:
            self.personal_info[info_type] = value
        else:
            raise ValueError(f"Question key '{info_type}' not found in selected questions.")

    def get_all_available_questions(self) -> Dict[str, List[Tuple[str, str]]]:
        """Get all available questions organized by category."""
        return self.question_pool.copy()

    def use_legacy_questions(self) -> None:
        """
        Set up the original four questions for backward compatibility.
        This ensures existing encrypted phrases can still be decrypted.
        """
        legacy_questions = [
            ('nickname', 'Enter your nickname: '),
            ('pin', 'Enter your PIN: '),
            ('first_love', 'Enter your first love\'s name: '),
            ('favorite_food', 'Enter your favorite food: ')
        ]

        self.selected_questions = {key: question for key, question in legacy_questions}
        self.personal_info = {key: '' for key, _ in legacy_questions}
            
    def get_random_separator(self) -> str:
        """Generate a random separator for passphrase parts."""
        return random.choice(self.separators)
    
    def transform_text(self, text: str, shift: int) -> str:
        """Transform text by shifting characters."""
        # Simple shift cipher for each character
        result = ''
        for c in text:
            if c.isalpha():
                # Determine the base (97 for lowercase, 65 for uppercase)
                base = 97 if c.islower() else 65
                # Shift the character, keeping it in the same case
                result += chr((ord(c) - base + shift) % 26 + base)
            elif c.isdigit():
                # For digits, shift within 0-9
                result += str((int(c) + shift) % 10)
            else:
                result += c
        return result
    
    def untransform_text(self, text: str, shift: int) -> str:
        """Reverse the transformation of text."""
        # Simple shift cipher in reverse
        result = ''
        for c in text:
            if c.isalpha():
                # Determine the base (97 for lowercase, 65 for uppercase)
                base = 97 if c.islower() else 65
                # Unshift the character, keeping it in the same case
                result += chr((ord(c) - base - shift) % 26 + base)
            elif c.isdigit():
                # For digits, unshift within 0-9
                result += str((int(c) - shift) % 10)
            else:
                result += c
        return result
    
    def encrypt_phrase(self, phrase: str) -> tuple[str, list[str], int]:
        """Encrypt a phrase by combining it with personal info in random ways."""
        # Verify we have all required information
        if not all(self.personal_info.values()):
            missing_keys = [key for key, value in self.personal_info.items() if not value]
            raise ValueError(f"All personal information must be set before encrypting. Missing: {missing_keys}")

        # Generate a random shift value (1-15)
        shift = random.randint(1, 15)

        # Transform all parts - now works with any selected questions
        transformed_parts = [('phrase', self.transform_text(phrase, shift))]

        # Add all personal info answers
        for key, value in self.personal_info.items():
            transformed_parts.append((key, self.transform_text(value, shift)))
        
        # Shuffle the order
        random.shuffle(transformed_parts)
        
        # Keep track of the order for decryption
        order = [label for label, _ in transformed_parts]
        
        # Build the result with random separators
        result = []
        for i, (_, part) in enumerate(transformed_parts):
            result.append(part)
            if i < len(transformed_parts) - 1:
                result.append(self.get_random_separator())
        
        return ''.join(result), order, shift
    
    @staticmethod
    def decrypt_phrase(encrypted: str, order: list[str], shift: int) -> str:
        """Decrypt an encrypted phrase using the original order of parts."""
        try:
            # Clean up inputs
            encrypted = encrypted.strip()
            order = [o.strip() for o in order]
            
            # Split by any of the common separators
            parts = []
            current_part = ''
            for char in encrypted:
                if char in '@#$%&*-+!':
                    if current_part:  # only append non-empty parts
                        parts.append(current_part)
                        current_part = ''
                else:
                    current_part += char
            if current_part:  # append the last part
                parts.append(current_part)
            
            print(f"Debug - Parts found: {parts}")  # Debug info
            print(f"Debug - Order provided: {order}")  # Debug info
            
            if len(parts) != len(order):
                raise ValueError(f"Number of parts ({len(parts)}) doesn't match order length ({len(order)})")
            
            # Find the position of the original phrase
            try:
                phrase_position = order.index('phrase')
            except ValueError:
                raise ValueError("'phrase' not found in order list")
            
            # Get the transformed phrase
            transformed_phrase = parts[phrase_position]
            
            # Create a temporary generator to use untransform_text
            temp_generator = PassphraseGenerator()
            
            # Untransform and return the phrase
            return temp_generator.untransform_text(transformed_phrase, shift)
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}") 