#!/usr/bin/env python3
"""
Demonstration script showing the new random question features.
This script shows how the enhanced PassphraseGenerator works without requiring user input.
"""

from passphrase_generator import Passphrase<PERSON>enerator

def demo_random_questions():
    """Demonstrate the random question selection feature."""
    print("=" * 60)
    print("DEMO: Random Question Selection")
    print("=" * 60)
    
    generator = PassphraseGenerator()
    
    print("1. Selecting 5 random questions from all categories:")
    selected_questions = generator.select_random_questions(5)
    
    for i, (key, question) in enumerate(selected_questions, 1):
        print(f"   {i}. {question}")
    
    print("\n2. Setting sample answers:")
    sample_answers = [
        "<PERSON><PERSON>ffy",
        "Springfield", 
        "Pizza",
        "Blue",
        "1234"
    ]
    
    for (key, _), answer in zip(selected_questions, sample_answers):
        generator.set_personal_info(key, answer)
        print(f"   {key}: {answer}")
    
    print("\n3. Encrypting a test phrase:")
    test_phrase = "MySecretPassword"
    encrypted, order, shift = generator.encrypt_phrase(test_phrase)
    
    print(f"   Original: {test_phrase}")
    print(f"   Encrypted: {encrypted}")
    print(f"   Order: {order}")
    print(f"   Shift: {shift}")
    
    print("\n4. Decrypting to verify:")
    decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
    print(f"   Decrypted: {decrypted}")
    print(f"   ✓ Success: {decrypted == test_phrase}")

def demo_legacy_compatibility():
    """Demonstrate backward compatibility with legacy questions."""
    print("\n" + "=" * 60)
    print("DEMO: Legacy Compatibility")
    print("=" * 60)
    
    generator = PassphraseGenerator()
    generator.use_legacy_questions()
    
    print("1. Using legacy questions for backward compatibility:")
    legacy_questions = generator.get_selected_questions()
    
    for key, question in legacy_questions.items():
        print(f"   {key}: {question}")
    
    print("\n2. Setting legacy answers:")
    legacy_answers = {
        'nickname': 'Johnny',
        'pin': '1234',
        'first_love': 'Sarah',
        'favorite_food': 'Pizza'
    }
    
    for key, value in legacy_answers.items():
        generator.set_personal_info(key, value)
        print(f"   {key}: {value}")
    
    print("\n3. This ensures old encrypted phrases can still be decrypted!")

def demo_question_categories():
    """Show the variety of questions available by category."""
    print("\n" + "=" * 60)
    print("DEMO: Question Categories and Variety")
    print("=" * 60)
    
    generator = PassphraseGenerator()
    all_questions = generator.get_all_available_questions()
    
    print("Available question categories:")
    
    for category, questions in all_questions.items():
        print(f"\n{category.upper()} ({len(questions)} questions):")
        for key, question in questions[:3]:  # Show first 3 questions
            print(f"   • {question}")
        if len(questions) > 3:
            print(f"   ... and {len(questions) - 3} more")

def demo_randomness():
    """Demonstrate that questions are truly random each time."""
    print("\n" + "=" * 60)
    print("DEMO: Randomness for Enhanced Security")
    print("=" * 60)
    
    print("Generating 3 different sets of 4 questions to show randomness:")
    
    for i in range(3):
        generator = PassphraseGenerator()
        selected = generator.select_random_questions(4)
        
        print(f"\nSet {i+1}:")
        for j, (key, question) in enumerate(selected, 1):
            print(f"   {j}. {question}")

def demo_security_benefits():
    """Explain the security benefits of the new system."""
    print("\n" + "=" * 60)
    print("SECURITY BENEFITS")
    print("=" * 60)
    
    benefits = [
        "🔄 RANDOMNESS: Different questions each time prevents pattern recognition",
        "📊 VARIETY: 36 total questions across 5 categories provide extensive options",
        "🔢 SCALABILITY: Choose 3-8 questions based on your security needs",
        "🔒 UNPREDICTABILITY: Attackers can't guess which questions were used",
        "⚡ BACKWARD COMPATIBLE: Old encrypted phrases still work with legacy mode",
        "🎯 CATEGORIZED: Questions span childhood, family, preferences, memories, and personal info"
    ]
    
    for benefit in benefits:
        print(f"   {benefit}")
    
    print(f"\n   📈 MATH: With 36 questions, choosing 4 gives {36*35*34*33} possible combinations!")

def main():
    """Run all demonstrations."""
    print("🔐 ENHANCED PASSPHRASE GENERATOR DEMONSTRATION")
    print("Showing new random question features for improved security")
    
    demo_random_questions()
    demo_legacy_compatibility()
    demo_question_categories()
    demo_randomness()
    demo_security_benefits()
    
    print("\n" + "=" * 60)
    print("✅ DEMONSTRATION COMPLETE")
    print("=" * 60)
    print("\nTo use the interactive version, run: python main.py")
    print("Choose option 1 for random questions or option 2 for legacy compatibility")

if __name__ == "__main__":
    main()
