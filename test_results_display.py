#!/usr/bin/env python3
"""
Test script to verify the results display works correctly after encryption.
"""

import tkinter as tk
from passphrase_gui import PassphraseG<PERSON>

def test_results_display():
    """Test that encryption results are properly displayed."""
    print("Testing results display functionality...")
    
    root = tk.Tk()
    root.withdraw()  # Hide window for testing
    
    try:
        # Create GUI
        app = PassphraseGUI(root)
        
        # Verify results components exist
        assert hasattr(app, 'results_text'), "Results text widget not found"
        assert hasattr(app, 'results_frame'), "Results frame not found"
        
        # Check initial placeholder text
        initial_text = app.results_text.get(1.0, tk.END).strip()
        assert "No encryption results yet" in initial_text, "Placeholder text not found"
        print("✅ Initial placeholder text is present")
        
        # Set up test encryption scenario
        app.generator.use_legacy_questions()
        app.current_questions = []
        
        # Create mock question entries
        for key in ['nickname', 'pin', 'first_love', 'favorite_food']:
            # Create a mock entry widget
            mock_entry = tk.Entry(root)
            mock_entry.insert(0, f"test_{key}")
            app.current_questions.append((key, mock_entry))
        
        # Set test phrase
        test_phrase = "TestPhrase123"
        app.phrase_entry.insert(0, test_phrase)
        
        print("✅ Test scenario set up")
        
        # Test the encryption method (simulate button click)
        app.encrypt_phrase()
        
        # Check that results were displayed
        results_text = app.results_text.get(1.0, tk.END)
        
        # Verify key components are in the results
        assert "ENCRYPTION SUCCESSFUL" in results_text, "Success message not found"
        assert test_phrase in results_text, "Original phrase not in results"
        assert "Encrypted Result:" in results_text, "Encrypted result section not found"
        assert "Order:" in results_text, "Order not in results"
        assert "Shift:" in results_text, "Shift not in results"
        assert "DECRYPTION KEYS" in results_text, "Decryption keys section not found"
        
        print("✅ Encryption results properly displayed!")
        print("✅ All required sections present in results")
        print("✅ Results text widget is functional")
        
        # Test that results are readable
        lines = results_text.split('\n')
        assert len(lines) > 10, "Results seem too short"
        
        print(f"✅ Results contain {len(lines)} lines of text")
        print("✅ Results display test passed!")
        
    except Exception as e:
        print(f"❌ Results display test failed: {e}")
        raise
    finally:
        root.destroy()

def test_results_visibility():
    """Test that the results area is properly visible and sized."""
    print("\nTesting results area visibility...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = PassphraseGUI(root)
        
        # Check that results frame is properly configured
        results_frame = app.results_frame
        results_text = app.results_text
        
        # Verify frame is packed
        pack_info = results_frame.pack_info()
        assert pack_info['fill'] == 'both', "Results frame not set to fill both"
        assert pack_info['expand'] == 1, "Results frame not set to expand"
        
        # Verify text widget configuration
        text_config = results_text.config()
        assert text_config['height'][4] >= 10, "Results text height too small"
        assert text_config['wrap'][4] == 'word', "Text wrapping not set correctly"
        
        print("✅ Results frame properly configured")
        print("✅ Results text widget properly sized")
        print("✅ Visibility test passed!")
        
    except Exception as e:
        print(f"❌ Visibility test failed: {e}")
        raise
    finally:
        root.destroy()

def main():
    """Run all results display tests."""
    print("=" * 60)
    print("TESTING RESULTS DISPLAY FUNCTIONALITY")
    print("=" * 60)
    
    try:
        test_results_display()
        test_results_visibility()
        
        print("\n" + "=" * 60)
        print("✅ ALL RESULTS DISPLAY TESTS PASSED!")
        print("=" * 60)
        print("\nResults display improvements:")
        print("• Larger, more visible results area")
        print("• Clear placeholder text with instructions")
        print("• Properly formatted encryption results")
        print("• Better visual separation of sections")
        print("• Read-only protection after display")
        print("• Expanded area that fills available space")
        print("\nThe results should now be clearly visible after encryption!")
        
    except Exception as e:
        print(f"\n❌ RESULTS DISPLAY TESTS FAILED: {e}")
        raise

if __name__ == "__main__":
    main()
