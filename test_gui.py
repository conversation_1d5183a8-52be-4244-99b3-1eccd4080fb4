#!/usr/bin/env python3
"""
Test script to verify GUI components work correctly.
This tests the GUI functionality without actually opening the window.
"""

import tkinter as tk
from passphrase_gui import PassphraseGUI
from passphrase_generator import PassphraseGenerator

def test_gui_initialization():
    """Test that the GUI initializes correctly."""
    print("Testing GUI initialization...")
    
    # Create a root window (but don't show it)
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    try:
        # Initialize the GUI
        app = PassphraseGUI(root)
        
        # Check that key components exist
        assert hasattr(app, 'generator'), "Generator not initialized"
        assert hasattr(app, 'notebook'), "Notebook not created"
        assert hasattr(app, 'mode_var'), "Mode variable not created"
        assert hasattr(app, 'num_questions_var'), "Number questions variable not created"
        
        print("✅ GUI initialization test passed!")
        
    except Exception as e:
        print(f"❌ GUI initialization test failed: {e}")
        raise
    finally:
        root.destroy()

def test_question_generation():
    """Test question generation functionality."""
    print("\nTesting question generation...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = PassphraseGUI(root)
        
        # Test random mode
        app.mode_var.set("random")
        app.num_questions_var.set(4)
        
        # This would normally be called by the button
        # We'll test the underlying logic
        generator = PassphraseGenerator()
        questions = generator.select_random_questions(4)
        
        assert len(questions) == 4, f"Expected 4 questions, got {len(questions)}"
        assert all(isinstance(q, tuple) and len(q) == 2 for q in questions), "Invalid question format"
        
        # Test legacy mode
        generator.use_legacy_questions()
        legacy_questions = generator.get_selected_questions()
        
        expected_keys = {'nickname', 'pin', 'first_love', 'favorite_food'}
        assert set(legacy_questions.keys()) == expected_keys, "Legacy questions not correct"
        
        print("✅ Question generation test passed!")
        
    except Exception as e:
        print(f"❌ Question generation test failed: {e}")
        raise
    finally:
        root.destroy()

def test_encryption_logic():
    """Test the encryption logic that the GUI uses."""
    print("\nTesting encryption logic...")
    
    try:
        generator = PassphraseGenerator()
        
        # Set up test questions and answers
        questions = generator.select_random_questions(3)
        test_answers = ["TestAnswer1", "TestAnswer2", "TestAnswer3"]
        
        for (key, _), answer in zip(questions, test_answers):
            generator.set_personal_info(key, answer)
        
        # Test encryption
        test_phrase = "TestPhrase123"
        encrypted, order, shift = generator.encrypt_phrase(test_phrase)
        
        # Verify encryption results
        assert isinstance(encrypted, str) and len(encrypted) > 0, "Encryption failed"
        assert isinstance(order, list) and len(order) > 0, "Order not generated"
        assert isinstance(shift, int) and 1 <= shift <= 15, "Invalid shift value"
        
        # Test decryption
        decrypted = PassphraseGenerator.decrypt_phrase(encrypted, order, shift)
        assert decrypted == test_phrase, f"Decryption failed: {decrypted} != {test_phrase}"
        
        print("✅ Encryption logic test passed!")
        
    except Exception as e:
        print(f"❌ Encryption logic test failed: {e}")
        raise

def test_gui_components():
    """Test that GUI components are properly configured."""
    print("\nTesting GUI components...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = PassphraseGUI(root)
        
        # Test that tabs exist
        assert app.notebook.index("end") >= 2, "Not enough tabs created"
        
        # Test mode variable
        assert app.mode_var.get() == "random", "Default mode not set correctly"
        
        # Test number of questions variable
        assert 3 <= app.num_questions_var.get() <= 8, "Invalid default question count"
        
        # Test that key frames exist
        assert hasattr(app, 'questions_frame'), "Questions frame not created"
        assert hasattr(app, 'results_frame'), "Results frame not created"
        
        print("✅ GUI components test passed!")
        
    except Exception as e:
        print(f"❌ GUI components test failed: {e}")
        raise
    finally:
        root.destroy()

def main():
    """Run all GUI tests."""
    print("=" * 50)
    print("TESTING PASSPHRASE GUI")
    print("=" * 50)
    
    try:
        test_gui_initialization()
        test_question_generation()
        test_encryption_logic()
        test_gui_components()
        
        print("\n" + "=" * 50)
        print("✅ ALL GUI TESTS PASSED!")
        print("=" * 50)
        print("\nThe GUI is ready to use!")
        print("Run: python passphrase_gui.py")
        
    except Exception as e:
        print(f"\n❌ GUI TESTS FAILED: {e}")
        print("Please check the error and try again.")
        raise

if __name__ == "__main__":
    main()
