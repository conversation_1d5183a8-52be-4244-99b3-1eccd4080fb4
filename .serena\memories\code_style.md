# Code Style and Conventions

## Python Style
- Type hints are used throughout the codebase
- Docstrings are used for classes and functions
- PEP 8 style guidelines are followed

## Naming Conventions
- Classes: PascalCase (e.g., `PassphraseGenerator`)
- Functions/Methods: snake_case (e.g., `get_user_input`, `set_personal_info`)
- Variables: snake_case
- Constants: UPPER_SNAKE_CASE

## Code Organization
- One class per file for main functionality
- Separate CLI interface from core logic
- Clear separation of concerns between user interaction and business logic

## Error Handling
- Explicit error messages
- ValueError for invalid inputs
- Try-except blocks for error handling in user interface