# Task Completion Checklist

When completing a task, ensure the following steps are taken:

1. **Code Quality**
   - [ ] Code follows PEP 8 style guidelines
   - [ ] Type hints are properly used
   - [ ] Docstrings are added for new functions/classes
   - [ ] No unused imports or variables
   - [ ] Proper error handling is implemented

2. **Testing**
   - [ ] Manual testing of new functionality
   - [ ] Edge cases are considered and handled
   - [ ] Error cases are tested
   - [ ] Integration with existing functionality is verified

3. **Documentation**
   - [ ] Code is self-documenting with clear names
   - [ ] Complex logic is commented
   - [ ] User-facing changes are documented

4. **Security**
   - [ ] No sensitive information is exposed
   - [ ] Input validation is implemented
   - [ ] Error messages don't expose internal details

5. **Performance**
   - [ ] Code is efficient
   - [ ] No unnecessary computations
   - [ ] Memory usage is reasonable